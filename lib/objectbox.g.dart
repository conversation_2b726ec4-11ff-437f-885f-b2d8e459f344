// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'features/home/<USER>/entities/tasks_response_entity.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
      id: const obx_int.IdUid(1, 4989293190837437690),
      name: '<PERSON><PERSON>',
      lastPropertyId: const obx_int.IdUid(48, 7695162489770509703),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 7866617021410304017),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 1917999897350191970),
            name: 'sentToPayroll',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 7187077056709341408),
            name: 'showKm',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 2023916243833773990),
            name: 'client',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 3402308568971675770),
            name: 'clientLogoUrl',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 7249967941954287075),
            name: 'storeGroup',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 304053710269816065),
            name: 'storeName',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 5250025709808758547),
            name: 'storeEmail',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(9, 8767957361764176694),
            name: 'comment',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(10, 178566632470600555),
            name: 'location',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(11, 8088654016980929219),
            name: 'suburb',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(12, 882861461136233436),
            name: 'cycle',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(13, 3477306333226801362),
            name: 'canDelete',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(14, 4729734751429998156),
            name: 'scheduledTimeStamp',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(15, 7586890520791944816),
            name: 'submissionTimeStamp',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(16, 1951837428018579443),
            name: 'expires',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(17, 2734605590803593356),
            name: 'onTask',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(18, 5738287678373096431),
            name: 'phone',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(19, 6013716830216632692),
            name: 'rangeStart',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(20, 8143288601352388700),
            name: 'rangeEnd',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(21, 3793041385269087492),
            name: 'reOpened',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(22, 6592628351744545318),
            name: 'reOpenedReason',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(23, 987612093206478672),
            name: 'taskStatus',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(24, 6753547410419319357),
            name: 'connoteUrl',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(25, 3718150895511086951),
            name: 'posRequired',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(26, 1357328974337357705),
            name: 'isPosMandatory',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(27, 792590189407773463),
            name: 'posReceived',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(28, 8769943795481164169),
            name: 'modifiedTimeStampDocuments',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(29, 1813013136704241794),
            name: 'modifiedTimeStampForms',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(30, 8351670303686273512),
            name: 'modifiedTimeStampMembers',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(31, 798021831892894409),
            name: 'modifiedTimeStampTask',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(32, 8482013567558532175),
            name: 'modifiedTimeStampPhotos',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(33, 6866832247594888783),
            name: 'modifiedTimeStampSignatures',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(34, 7929727129314586342),
            name: 'modifiedTimeStampSignaturetypes',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(35, 761112182931492822),
            name: 'posSentTo',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(36, 3749350646960920860),
            name: 'posSentToEmail',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(37, 4859840613507767592),
            name: 'modifiedTimeStampPhototypes',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(38, 4634278113742781728),
            name: 'taskCommencementTimeStamp',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(39, 7486506732435461602),
            name: 'taskStoppedTimeStamp',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(40, 1291126189314419769),
            name: 'taskNote',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(41, 2674413479691419693),
            name: 'disallowReschedule',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(42, 3873519663078325006),
            name: 'liveImagesOnly',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(43, 8111976551720558409),
            name: 'timeSchedule',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(44, 6374639905880152861),
            name: 'showFollowupIconMulti',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(45, 3037716298768107755),
            name: 'followupSelectedMulti',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(46, 1273187969640999677),
            name: 'isOpen',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(47, 542456641046319776),
            name: 'preftime',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(48, 7695162489770509703),
            name: 'sendTo',
            type: 1,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[])
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore(
    {String? directory,
    int? maxDBSizeInKB,
    int? maxDataSizeInKB,
    int? fileMode,
    int? maxReaders,
    bool queriesCaseSensitiveDefault = true,
    String? macosApplicationGroup}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(getObjectBoxModel(),
      directory: directory ?? (await defaultStoreDirectory()).path,
      maxDBSizeInKB: maxDBSizeInKB,
      maxDataSizeInKB: maxDataSizeInKB,
      fileMode: fileMode,
      maxReaders: maxReaders,
      queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
      macosApplicationGroup: macosApplicationGroup);
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
      entities: _entities,
      lastEntityId: const obx_int.IdUid(1, 4989293190837437690),
      lastIndexId: const obx_int.IdUid(0, 0),
      lastRelationId: const obx_int.IdUid(0, 0),
      lastSequenceId: const obx_int.IdUid(0, 0),
      retiredEntityUids: const [],
      retiredIndexUids: const [],
      retiredPropertyUids: const [],
      retiredRelationUids: const [],
      modelVersion: 5,
      modelVersionParserMinimum: 5,
      version: 1);

  final bindings = <Type, obx_int.EntityDefinition>{
    Datum: obx_int.EntityDefinition<Datum>(
        model: _entities[0],
        toOneRelations: (Datum object) => [],
        toManyRelations: (Datum object) => {},
        getId: (Datum object) => object.id,
        setId: (Datum object, int id) {
          object.id = id;
        },
        objectToFB: (Datum object, fb.Builder fbb) {
          final clientOffset =
              object.client == null ? null : fbb.writeString(object.client!);
          final clientLogoUrlOffset = object.clientLogoUrl == null
              ? null
              : fbb.writeString(object.clientLogoUrl!);
          final storeGroupOffset = object.storeGroup == null
              ? null
              : fbb.writeString(object.storeGroup!);
          final storeNameOffset = object.storeName == null
              ? null
              : fbb.writeString(object.storeName!);
          final storeEmailOffset = object.storeEmail == null
              ? null
              : fbb.writeString(object.storeEmail!);
          final commentOffset =
              object.comment == null ? null : fbb.writeString(object.comment!);
          final locationOffset = object.location == null
              ? null
              : fbb.writeString(object.location!);
          final suburbOffset =
              object.suburb == null ? null : fbb.writeString(object.suburb!);
          final cycleOffset =
              object.cycle == null ? null : fbb.writeString(object.cycle!);
          final onTaskOffset =
              object.onTask == null ? null : fbb.writeString(object.onTask!);
          final phoneOffset =
              object.phone == null ? null : fbb.writeString(object.phone!);
          final reOpenedReasonOffset = object.reOpenedReason == null
              ? null
              : fbb.writeString(object.reOpenedReason!);
          final taskStatusOffset = object.taskStatus == null
              ? null
              : fbb.writeString(object.taskStatus!);
          final connoteUrlOffset = object.connoteUrl == null
              ? null
              : fbb.writeString(object.connoteUrl!);
          final posReceivedOffset = object.posReceived == null
              ? null
              : fbb.writeString(object.posReceived!);
          final posSentToOffset = object.posSentTo == null
              ? null
              : fbb.writeString(object.posSentTo!);
          final posSentToEmailOffset = object.posSentToEmail == null
              ? null
              : fbb.writeString(object.posSentToEmail!);
          final taskNoteOffset = object.taskNote == null
              ? null
              : fbb.writeString(object.taskNote!);
          final timeScheduleOffset = object.timeSchedule == null
              ? null
              : fbb.writeString(object.timeSchedule!);
          final preftimeOffset = object.preftime == null
              ? null
              : fbb.writeString(object.preftime!);
          fbb.startTable(49);
          fbb.addInt64(0, object.id);
          fbb.addBool(1, object.sentToPayroll);
          fbb.addBool(2, object.showKm);
          fbb.addOffset(3, clientOffset);
          fbb.addOffset(4, clientLogoUrlOffset);
          fbb.addOffset(5, storeGroupOffset);
          fbb.addOffset(6, storeNameOffset);
          fbb.addOffset(7, storeEmailOffset);
          fbb.addOffset(8, commentOffset);
          fbb.addOffset(9, locationOffset);
          fbb.addOffset(10, suburbOffset);
          fbb.addOffset(11, cycleOffset);
          fbb.addBool(12, object.canDelete);
          fbb.addInt64(13, object.scheduledTimeStamp?.millisecondsSinceEpoch);
          fbb.addInt64(14, object.submissionTimeStamp?.millisecondsSinceEpoch);
          fbb.addInt64(15, object.expires?.millisecondsSinceEpoch);
          fbb.addOffset(16, onTaskOffset);
          fbb.addOffset(17, phoneOffset);
          fbb.addInt64(18, object.rangeStart?.millisecondsSinceEpoch);
          fbb.addInt64(19, object.rangeEnd?.millisecondsSinceEpoch);
          fbb.addBool(20, object.reOpened);
          fbb.addOffset(21, reOpenedReasonOffset);
          fbb.addOffset(22, taskStatusOffset);
          fbb.addOffset(23, connoteUrlOffset);
          fbb.addBool(24, object.posRequired);
          fbb.addBool(25, object.isPosMandatory);
          fbb.addOffset(26, posReceivedOffset);
          fbb.addInt64(
              27, object.modifiedTimeStampDocuments?.millisecondsSinceEpoch);
          fbb.addInt64(
              28, object.modifiedTimeStampForms?.millisecondsSinceEpoch);
          fbb.addInt64(
              29, object.modifiedTimeStampMembers?.millisecondsSinceEpoch);
          fbb.addInt64(
              30, object.modifiedTimeStampTask?.millisecondsSinceEpoch);
          fbb.addInt64(
              31, object.modifiedTimeStampPhotos?.millisecondsSinceEpoch);
          fbb.addInt64(
              32, object.modifiedTimeStampSignatures?.millisecondsSinceEpoch);
          fbb.addInt64(33,
              object.modifiedTimeStampSignaturetypes?.millisecondsSinceEpoch);
          fbb.addOffset(34, posSentToOffset);
          fbb.addOffset(35, posSentToEmailOffset);
          fbb.addInt64(
              36, object.modifiedTimeStampPhototypes?.millisecondsSinceEpoch);
          fbb.addInt64(
              37, object.taskCommencementTimeStamp?.millisecondsSinceEpoch);
          fbb.addInt64(38, object.taskStoppedTimeStamp?.millisecondsSinceEpoch);
          fbb.addOffset(39, taskNoteOffset);
          fbb.addBool(40, object.disallowReschedule);
          fbb.addBool(41, object.liveImagesOnly);
          fbb.addOffset(42, timeScheduleOffset);
          fbb.addBool(43, object.showFollowupIconMulti);
          fbb.addBool(44, object.followupSelectedMulti);
          fbb.addBool(45, object.isOpen);
          fbb.addOffset(46, preftimeOffset);
          fbb.addBool(47, object.sendTo);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final scheduledTimeStampValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 30);
          final submissionTimeStampValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 32);
          final expiresValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 34);
          final rangeStartValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 40);
          final rangeEndValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 42);
          final modifiedTimeStampDocumentsValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 58);
          final modifiedTimeStampFormsValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 60);
          final modifiedTimeStampMembersValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 62);
          final modifiedTimeStampTaskValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 64);
          final modifiedTimeStampPhotosValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 66);
          final modifiedTimeStampSignaturesValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 68);
          final modifiedTimeStampSignaturetypesValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 70);
          final modifiedTimeStampPhototypesValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 76);
          final taskCommencementTimeStampValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 78);
          final taskStoppedTimeStampValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 80);
          final sentToPayrollParam =
              const fb.BoolReader().vTableGetNullable(buffer, rootOffset, 6);
          final showKmParam =
              const fb.BoolReader().vTableGetNullable(buffer, rootOffset, 8);
          final clientParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 10);
          final clientLogoUrlParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 12);
          final storeGroupParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 14);
          final storeNameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 16);
          final storeEmailParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 18);
          final commentParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 20);
          final locationParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 22);
          final suburbParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 24);
          final cycleParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 26);
          final canDeleteParam =
              const fb.BoolReader().vTableGetNullable(buffer, rootOffset, 28);
          final scheduledTimeStampParam = scheduledTimeStampValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(scheduledTimeStampValue);
          final submissionTimeStampParam = submissionTimeStampValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(submissionTimeStampValue);
          final expiresParam = expiresValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(expiresValue);
          final onTaskParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 36);
          final phoneParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 38);
          final rangeStartParam = rangeStartValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(rangeStartValue);
          final rangeEndParam = rangeEndValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(rangeEndValue);
          final reOpenedParam =
              const fb.BoolReader().vTableGetNullable(buffer, rootOffset, 44);
          final reOpenedReasonParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 46);
          final taskStatusParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 48);
          final connoteUrlParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 50);
          final posRequiredParam =
              const fb.BoolReader().vTableGetNullable(buffer, rootOffset, 52);
          final isPosMandatoryParam =
              const fb.BoolReader().vTableGetNullable(buffer, rootOffset, 54);
          final posReceivedParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 56);
          final modifiedTimeStampDocumentsParam =
              modifiedTimeStampDocumentsValue == null
                  ? null
                  : DateTime.fromMillisecondsSinceEpoch(
                      modifiedTimeStampDocumentsValue);
          final modifiedTimeStampFormsParam =
              modifiedTimeStampFormsValue == null
                  ? null
                  : DateTime.fromMillisecondsSinceEpoch(
                      modifiedTimeStampFormsValue);
          final modifiedTimeStampMembersParam =
              modifiedTimeStampMembersValue == null
                  ? null
                  : DateTime.fromMillisecondsSinceEpoch(
                      modifiedTimeStampMembersValue);
          final modifiedTimeStampTaskParam = modifiedTimeStampTaskValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(modifiedTimeStampTaskValue);
          final modifiedTimeStampPhotosParam =
              modifiedTimeStampPhotosValue == null
                  ? null
                  : DateTime.fromMillisecondsSinceEpoch(
                      modifiedTimeStampPhotosValue);
          final modifiedTimeStampSignaturesParam =
              modifiedTimeStampSignaturesValue == null
                  ? null
                  : DateTime.fromMillisecondsSinceEpoch(
                      modifiedTimeStampSignaturesValue);
          final modifiedTimeStampSignaturetypesParam =
              modifiedTimeStampSignaturetypesValue == null
                  ? null
                  : DateTime.fromMillisecondsSinceEpoch(
                      modifiedTimeStampSignaturetypesValue);
          final posSentToParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 72);
          final posSentToEmailParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 74);
          final modifiedTimeStampPhototypesParam =
              modifiedTimeStampPhototypesValue == null
                  ? null
                  : DateTime.fromMillisecondsSinceEpoch(
                      modifiedTimeStampPhototypesValue);
          final taskCommencementTimeStampParam =
              taskCommencementTimeStampValue == null
                  ? null
                  : DateTime.fromMillisecondsSinceEpoch(
                      taskCommencementTimeStampValue);
          final taskStoppedTimeStampParam = taskStoppedTimeStampValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(taskStoppedTimeStampValue);
          final taskNoteParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 82);
          final disallowRescheduleParam =
              const fb.BoolReader().vTableGetNullable(buffer, rootOffset, 84);
          final liveImagesOnlyParam =
              const fb.BoolReader().vTableGetNullable(buffer, rootOffset, 86);
          final timeScheduleParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGetNullable(buffer, rootOffset, 88);
          final showFollowupIconMultiParam =
              const fb.BoolReader().vTableGetNullable(buffer, rootOffset, 90);
          final followupSelectedMultiParam =
              const fb.BoolReader().vTableGetNullable(buffer, rootOffset, 92);
          final isOpenParam =
              const fb.BoolReader().vTableGetNullable(buffer, rootOffset, 94);
          final preftimeParam = const fb.StringReader(asciiOptimization: true)
              .vTableGetNullable(buffer, rootOffset, 96);
          final sendToParam =
              const fb.BoolReader().vTableGetNullable(buffer, rootOffset, 98);
          final object = Datum(
              sentToPayroll: sentToPayrollParam,
              showKm: showKmParam,
              client: clientParam,
              clientLogoUrl: clientLogoUrlParam,
              storeGroup: storeGroupParam,
              storeName: storeNameParam,
              storeEmail: storeEmailParam,
              comment: commentParam,
              location: locationParam,
              suburb: suburbParam,
              cycle: cycleParam,
              canDelete: canDeleteParam,
              scheduledTimeStamp: scheduledTimeStampParam,
              submissionTimeStamp: submissionTimeStampParam,
              expires: expiresParam,
              onTask: onTaskParam,
              phone: phoneParam,
              rangeStart: rangeStartParam,
              rangeEnd: rangeEndParam,
              reOpened: reOpenedParam,
              reOpenedReason: reOpenedReasonParam,
              taskStatus: taskStatusParam,
              connoteUrl: connoteUrlParam,
              posRequired: posRequiredParam,
              isPosMandatory: isPosMandatoryParam,
              posReceived: posReceivedParam,
              modifiedTimeStampDocuments: modifiedTimeStampDocumentsParam,
              modifiedTimeStampForms: modifiedTimeStampFormsParam,
              modifiedTimeStampMembers: modifiedTimeStampMembersParam,
              modifiedTimeStampTask: modifiedTimeStampTaskParam,
              modifiedTimeStampPhotos: modifiedTimeStampPhotosParam,
              modifiedTimeStampSignatures: modifiedTimeStampSignaturesParam,
              modifiedTimeStampSignaturetypes:
                  modifiedTimeStampSignaturetypesParam,
              posSentTo: posSentToParam,
              posSentToEmail: posSentToEmailParam,
              modifiedTimeStampPhototypes: modifiedTimeStampPhototypesParam,
              taskCommencementTimeStamp: taskCommencementTimeStampParam,
              taskStoppedTimeStamp: taskStoppedTimeStampParam,
              taskNote: taskNoteParam,
              disallowReschedule: disallowRescheduleParam,
              liveImagesOnly: liveImagesOnlyParam,
              timeSchedule: timeScheduleParam,
              showFollowupIconMulti: showFollowupIconMultiParam,
              followupSelectedMulti: followupSelectedMultiParam,
              isOpen: isOpenParam,
              preftime: preftimeParam,
              sendTo: sendToParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        })
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [Datum] entity fields to define ObjectBox queries.
class Datum_ {
  /// See [Datum.id].
  static final id = obx.QueryIntegerProperty<Datum>(_entities[0].properties[0]);

  /// See [Datum.sentToPayroll].
  static final sentToPayroll =
      obx.QueryBooleanProperty<Datum>(_entities[0].properties[1]);

  /// See [Datum.showKm].
  static final showKm =
      obx.QueryBooleanProperty<Datum>(_entities[0].properties[2]);

  /// See [Datum.client].
  static final client =
      obx.QueryStringProperty<Datum>(_entities[0].properties[3]);

  /// See [Datum.clientLogoUrl].
  static final clientLogoUrl =
      obx.QueryStringProperty<Datum>(_entities[0].properties[4]);

  /// See [Datum.storeGroup].
  static final storeGroup =
      obx.QueryStringProperty<Datum>(_entities[0].properties[5]);

  /// See [Datum.storeName].
  static final storeName =
      obx.QueryStringProperty<Datum>(_entities[0].properties[6]);

  /// See [Datum.storeEmail].
  static final storeEmail =
      obx.QueryStringProperty<Datum>(_entities[0].properties[7]);

  /// See [Datum.comment].
  static final comment =
      obx.QueryStringProperty<Datum>(_entities[0].properties[8]);

  /// See [Datum.location].
  static final location =
      obx.QueryStringProperty<Datum>(_entities[0].properties[9]);

  /// See [Datum.suburb].
  static final suburb =
      obx.QueryStringProperty<Datum>(_entities[0].properties[10]);

  /// See [Datum.cycle].
  static final cycle =
      obx.QueryStringProperty<Datum>(_entities[0].properties[11]);

  /// See [Datum.canDelete].
  static final canDelete =
      obx.QueryBooleanProperty<Datum>(_entities[0].properties[12]);

  /// See [Datum.scheduledTimeStamp].
  static final scheduledTimeStamp =
      obx.QueryDateProperty<Datum>(_entities[0].properties[13]);

  /// See [Datum.submissionTimeStamp].
  static final submissionTimeStamp =
      obx.QueryDateProperty<Datum>(_entities[0].properties[14]);

  /// See [Datum.expires].
  static final expires =
      obx.QueryDateProperty<Datum>(_entities[0].properties[15]);

  /// See [Datum.onTask].
  static final onTask =
      obx.QueryStringProperty<Datum>(_entities[0].properties[16]);

  /// See [Datum.phone].
  static final phone =
      obx.QueryStringProperty<Datum>(_entities[0].properties[17]);

  /// See [Datum.rangeStart].
  static final rangeStart =
      obx.QueryDateProperty<Datum>(_entities[0].properties[18]);

  /// See [Datum.rangeEnd].
  static final rangeEnd =
      obx.QueryDateProperty<Datum>(_entities[0].properties[19]);

  /// See [Datum.reOpened].
  static final reOpened =
      obx.QueryBooleanProperty<Datum>(_entities[0].properties[20]);

  /// See [Datum.reOpenedReason].
  static final reOpenedReason =
      obx.QueryStringProperty<Datum>(_entities[0].properties[21]);

  /// See [Datum.taskStatus].
  static final taskStatus =
      obx.QueryStringProperty<Datum>(_entities[0].properties[22]);

  /// See [Datum.connoteUrl].
  static final connoteUrl =
      obx.QueryStringProperty<Datum>(_entities[0].properties[23]);

  /// See [Datum.posRequired].
  static final posRequired =
      obx.QueryBooleanProperty<Datum>(_entities[0].properties[24]);

  /// See [Datum.isPosMandatory].
  static final isPosMandatory =
      obx.QueryBooleanProperty<Datum>(_entities[0].properties[25]);

  /// See [Datum.posReceived].
  static final posReceived =
      obx.QueryStringProperty<Datum>(_entities[0].properties[26]);

  /// See [Datum.modifiedTimeStampDocuments].
  static final modifiedTimeStampDocuments =
      obx.QueryDateProperty<Datum>(_entities[0].properties[27]);

  /// See [Datum.modifiedTimeStampForms].
  static final modifiedTimeStampForms =
      obx.QueryDateProperty<Datum>(_entities[0].properties[28]);

  /// See [Datum.modifiedTimeStampMembers].
  static final modifiedTimeStampMembers =
      obx.QueryDateProperty<Datum>(_entities[0].properties[29]);

  /// See [Datum.modifiedTimeStampTask].
  static final modifiedTimeStampTask =
      obx.QueryDateProperty<Datum>(_entities[0].properties[30]);

  /// See [Datum.modifiedTimeStampPhotos].
  static final modifiedTimeStampPhotos =
      obx.QueryDateProperty<Datum>(_entities[0].properties[31]);

  /// See [Datum.modifiedTimeStampSignatures].
  static final modifiedTimeStampSignatures =
      obx.QueryDateProperty<Datum>(_entities[0].properties[32]);

  /// See [Datum.modifiedTimeStampSignaturetypes].
  static final modifiedTimeStampSignaturetypes =
      obx.QueryDateProperty<Datum>(_entities[0].properties[33]);

  /// See [Datum.posSentTo].
  static final posSentTo =
      obx.QueryStringProperty<Datum>(_entities[0].properties[34]);

  /// See [Datum.posSentToEmail].
  static final posSentToEmail =
      obx.QueryStringProperty<Datum>(_entities[0].properties[35]);

  /// See [Datum.modifiedTimeStampPhototypes].
  static final modifiedTimeStampPhototypes =
      obx.QueryDateProperty<Datum>(_entities[0].properties[36]);

  /// See [Datum.taskCommencementTimeStamp].
  static final taskCommencementTimeStamp =
      obx.QueryDateProperty<Datum>(_entities[0].properties[37]);

  /// See [Datum.taskStoppedTimeStamp].
  static final taskStoppedTimeStamp =
      obx.QueryDateProperty<Datum>(_entities[0].properties[38]);

  /// See [Datum.taskNote].
  static final taskNote =
      obx.QueryStringProperty<Datum>(_entities[0].properties[39]);

  /// See [Datum.disallowReschedule].
  static final disallowReschedule =
      obx.QueryBooleanProperty<Datum>(_entities[0].properties[40]);

  /// See [Datum.liveImagesOnly].
  static final liveImagesOnly =
      obx.QueryBooleanProperty<Datum>(_entities[0].properties[41]);

  /// See [Datum.timeSchedule].
  static final timeSchedule =
      obx.QueryStringProperty<Datum>(_entities[0].properties[42]);

  /// See [Datum.showFollowupIconMulti].
  static final showFollowupIconMulti =
      obx.QueryBooleanProperty<Datum>(_entities[0].properties[43]);

  /// See [Datum.followupSelectedMulti].
  static final followupSelectedMulti =
      obx.QueryBooleanProperty<Datum>(_entities[0].properties[44]);

  /// See [Datum.isOpen].
  static final isOpen =
      obx.QueryBooleanProperty<Datum>(_entities[0].properties[45]);

  /// See [Datum.preftime].
  static final preftime =
      obx.QueryStringProperty<Datum>(_entities[0].properties[46]);

  /// See [Datum.sendTo].
  static final sendTo =
      obx.QueryBooleanProperty<Datum>(_entities[0].properties[47]);
}
