import 'package:objectbox/objectbox.dart';

import '../../../../core/database/objectbox.dart';
import '../../domain/entities/tasks_response_entity.dart';

abstract class HomeLocalDataSource {
  Future<void> saveUnscheduleResponse(TasksResponseEntity response);
  Future<TasksResponseEntity?> getUnscheduleResponse();
}

class HomeLocalDataSourceImpl implements HomeLocalDataSource {
  final ObjectBox objectBox;
  late Box<Datum> datumBox;

  HomeLocalDataSourceImpl(this.objectBox);

  @override
  Future<void> saveUnscheduleResponse(TasksResponseEntity response) async {
    // datumBox = objectBox.store.box<Datum>();

    // // Clear existing data
    // datumBox.removeAll();

    // // Save new data
    // var allTasks = response.data?["add_tasks"] ?? [];
    // for (var task in allTasks) {
    //   datumBox.put(task);
    // }
  }

  @override
  Future<TasksResponseEntity?> getUnscheduleResponse() async {
    // Implement fetching logic here
    return null;
  }
}
