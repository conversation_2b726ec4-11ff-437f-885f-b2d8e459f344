import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart' hide Form;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/constants/app_assets.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/core/utils/date_time_utils.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_request_entity.dart'
    as submit_report;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as schedule;
import 'package:storetrack_app/features/home/<USER>/blocs/schedule/schedule_cubit.dart';
import 'package:storetrack_app/features/home/<USER>/blocs/schedule/schedule_state.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';

import '../../../../config/routes/app_router.gr.dart';
import '../../../../core/storage/data_manager.dart';
import '../../domain/entities/tasks_response_entity.dart';
import '../../../home/<USER>/constants/action_types.dart';
import '../../../home/<USER>/widgets/calendar_bottom_sheet.dart';
import '../../../home/<USER>/widgets/reorderable_store_list.dart';
import '../widgets/empty.dart';

@RoutePage()
class SchedulePage extends StatefulWidget {
  const SchedulePage({super.key});

  @override
  State<SchedulePage> createState() => _SchedulePageState();
}

class _SchedulePageState extends State<SchedulePage>
    with SingleTickerProviderStateMixin {
  _SchedulePageState(); // Add an unnamed constructor
  late TabController _tabController;
  bool isWeekView = true;
  late DateTime selectedDate; // Using late to initialize in initState
  DateTime?
      selectedCalendarDate; // This will track which date is selected in the calendar
  List<DateTime> weekDays = [];
  final DateTime _currentMonth = DateTime.now();
  List<DateTime> monthDays = [];

  // Track which button is currently selected
  String _selectedButton =
      'all'; // Options: 'all', 'this_week', 'next_week', 'overdue'

  // Task data
  final String actualDeviceUid = "8b7a6774c878a206";
  late String actualUserId;
  final String actualAppVersion = "9.9.9";
  final List<String> actualTasksToSchedule = [];
  late String actualUserToken;

  List<Datum> scheduledTasks = [];
  bool _isCheckboxMode = false;
  List<Datum> selectedItems = [];
  bool _areAllItemsSelected = false;

  // Store all tasks from the API response
  List<schedule.Datum> allApiTasks = [];

  // Map to track which dates have tasks
  Map<String, bool> datesWithTasks = {};

  // Convert schedule.Datum to unscheduled Datum
  Datum convertScheduleToDatum(schedule.Datum scheduleTask) {
    // Create a Datum with all necessary properties to prevent null values
    return Datum(
      taskId: scheduleTask.taskId,
      taskStatus: scheduleTask.taskStatus,
      isOpen: scheduleTask.isOpen,
      storeName: scheduleTask.storeName,
      location: scheduleTask.location,
      scheduledTimeStamp: scheduleTask.scheduledTimeStamp,
      taskNote: scheduleTask.taskNote,
      posRequired: scheduleTask.posRequired,
      taskCount: scheduleTask.taskCount,
      teamlead: scheduleTask.teamlead,
      storeId: scheduleTask.storeId,
      client: scheduleTask.client,
      clientId: scheduleTask.clientId,
      clientLogoUrl: scheduleTask.clientLogoUrl,
      minutes: scheduleTask.minutes,
      budget: scheduleTask.budget,
      comment: scheduleTask.comment,
      suburb: scheduleTask.suburb,
      latitude: scheduleTask.latitude,
      longitude: scheduleTask.longitude,
      taskLatitude: scheduleTask.taskLatitude,
      taskLongitude: scheduleTask.taskLongitude,
      cycle: scheduleTask.cycle,
      cycleId: scheduleTask.cycleId,
      canDelete: scheduleTask.canDelete,
      submissionTimeStamp: scheduleTask.submissionTimeStamp,
      expires: scheduleTask.expires,
      onTask: scheduleTask.onTask,
      phone: scheduleTask.phone,
      rangeStart: scheduleTask.rangeStart,
      rangeEnd: scheduleTask.rangeEnd,
      reOpened: scheduleTask.reOpened,
      reOpenedReason: scheduleTask.reOpenedReason,
      warehousejobId: scheduleTask.warehousejobId,
      connoteUrl: scheduleTask.connoteUrl,
      isPosMandatory: scheduleTask.isPosMandatory,
      posReceived: scheduleTask.posReceived,
      // photoFolder: scheduleTask.photoFolder != null ? [] : null,
      // signatureFolder: scheduleTask.signatureFolder != null ? [] : null,
      // forms: scheduleTask.forms != null ? [] : null,
      // posItems: scheduleTask.posItems != null ? [] : null,
      // documents: scheduleTask.documents != null ? [] : null,
      // taskalerts: scheduleTask.taskalerts != null ? [] : null,
      // taskmembers: scheduleTask.taskmembers != null ? [] : null,
      modifiedTimeStampDocuments: scheduleTask.modifiedTimeStampDocuments,
      modifiedTimeStampForms: scheduleTask.modifiedTimeStampForms,
      modifiedTimeStampMembers: scheduleTask.modifiedTimeStampMembers,
      modifiedTimeStampTask: scheduleTask.modifiedTimeStampTask,
      modifiedTimeStampPhotos: scheduleTask.modifiedTimeStampPhotos,
      modifiedTimeStampSignatures: scheduleTask.modifiedTimeStampSignatures,
      modifiedTimeStampSignaturetypes:
          scheduleTask.modifiedTimeStampSignaturetypes,
      posSentTo: scheduleTask.posSentTo,
      posSentToEmail: scheduleTask.posSentToEmail,
      modifiedTimeStampPhototypes: scheduleTask.modifiedTimeStampPhototypes,
      taskCommencementTimeStamp: scheduleTask.taskCommencementTimeStamp,
      taskStoppedTimeStamp: scheduleTask.taskStoppedTimeStamp,
      // followupTasks: scheduleTask.followupTasks != null ? [] : null,
      // stocktake: scheduleTask.stocktake != null ? [] : null,
      // Convert empty lists to non-null empty lists to prevent null pointer exceptions
    );
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(_handleTabChange);

    // Set initial selected button based on tab
    _selectedButton = _tabController.index == 0
        ? 'all'
        : _tabController.index == 1
            ? 'this_week'
            : _tabController.index == 2
                ? 'next_week'
                : 'overdue';

    // Initialize selectedDate but don't select current date
    selectedDate = DateTime
        .now(); // We need to initialize it, but won't mark it as selected in the calendar

    // Initialize the week and month days
    _generateWeekDays();
    _generateMonthDays();

    // Initialize data
    _initializeData();
  }

  void _generateWeekDays() {
    // Get the start of the week (Monday)
    final DateTime now = selectedDate;
    final int day = now.weekday;
    final DateTime firstDayOfWeek = now.subtract(Duration(days: day - 1));

    weekDays = List.generate(7, (index) {
      return firstDayOfWeek.add(Duration(days: index));
    });

    // Check if today is in this week, if so select it
    final today = DateTime.now();
    final bool isTodayInWeek = weekDays.any((day) =>
        day.day == today.day &&
        day.month == today.month &&
        day.year == today.year);

    if (isTodayInWeek) {
      // Select today
      selectedDate = today;
    }
  }

  void _generateMonthDays() {
    // Get the first day of the month
    final DateTime firstDayOfMonth =
        DateTime(_currentMonth.year, _currentMonth.month, 1);

    // Get the weekday of the first day (0 = Monday, 6 = Sunday)
    final int firstWeekday = firstDayOfMonth.weekday - 1;

    // Calculate days from previous month to show
    final DateTime firstDayToShow =
        firstDayOfMonth.subtract(Duration(days: firstWeekday));

    // Calculate total days to show (6 weeks = 42 days)
    monthDays = List.generate(42, (index) {
      return firstDayToShow.add(Duration(days: index));
    });
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        // Update the selected button based on tab index
        if (_tabController.index == 0) {
          _selectedButton = 'all';
          isWeekView = true;

          // When switching to all view, we don't need to filter by date
          // as we'll show all scheduled tasks
          selectedDate = DateTime.now();
          selectedCalendarDate = null;
        } else if (_tabController.index == 1) {
          _selectedButton = 'this_week';
          isWeekView = true;

          // When switching to this week view, use today's date for filtering
          selectedDate = DateTime.now();
          selectedCalendarDate = null;
          _generateWeekDays();
        } else if (_tabController.index == 2) {
          _selectedButton = 'next_week';
          isWeekView = true;

          // When switching to next week view, use next week's date for filtering
          final DateTime nextWeekDate =
              DateTime.now().add(const Duration(days: 7));
          selectedDate = nextWeekDate;
          selectedCalendarDate = null;
          _generateWeekDays();
        } else if (_tabController.index == 3) {
          _selectedButton = 'overdue';
          isWeekView = false;
        }

        // For tabs other than "All", filter tasks for the selected date
        // For "All" tab, we'll show all scheduled tasks in _buildAllTasksListContent
        if (_selectedButton != 'all') {
          _filterTasksForSelectedDate();
        }
      });
    }
  }

  // Filter tasks for the selected date without making a new API call
  void _filterTasksForSelectedDate() {
    if (allApiTasks.isEmpty) {
      // If we don't have any data yet, we need to make an API call
      _initializeData();
      return;
    }

    // Filter scheduled tasks for the selected date
    var filteredTasks = allApiTasks.where((task) {
      // Check if the task is scheduled for the selected date
      return _isTaskScheduledForDate(task, selectedDate);
    }).toList()
      ..sort((a, b) => (a.storeName ?? '').compareTo(b.storeName ?? ''))
      ..sort((a, b) => (a.scheduledTimeStamp ?? DateTime.now())
          .compareTo(b.scheduledTimeStamp ?? DateTime.now()));

    // Convert schedule.Datum to unscheduled Datum for compatibility with StoreReorderableListNew
    setState(() {
      scheduledTasks =
          filteredTasks.map((task) => convertScheduleToDatum(task)).toList();
    });
  }

  Future<void> _initializeData() async {
    try {
      // Get user ID and token from DataManager
      actualUserId = await sl<DataManager>().getUserId() ?? "0";
      actualUserToken = await sl<DataManager>().getAuthToken() ?? "0";

      // Fetch both calendar and task data in a single call
      if (mounted) {
        context.read<ScheduleTaskCubit>().getData(
              TasksRequestEntity(
                deviceUid: actualDeviceUid,
                userId: actualUserId,
                appversion: actualAppVersion,
                tasks: const [],
                token: actualUserToken,
              ),
            );
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to initialize: ${e.toString()}',
        );
      }
    }
  }

  // Helper method to check if a task is scheduled for a specific date
  bool _isTaskScheduledForDate(schedule.Datum task, DateTime date) {
    // Check if the task has a scheduled timestamp
    if (task.scheduledTimeStamp == null) return false;

    // Check if the task is confirmed and not open
    if (task.taskStatus != "Confirmed" || task.isOpen == true) return false;

    // Compare only the date part (year, month, day) ignoring time
    final taskDate = task.scheduledTimeStamp!;
    final taskDateOnly = DateTime(taskDate.year, taskDate.month, taskDate.day);
    final selectedDateOnly = DateTime(date.year, date.month, date.day);

    // Check if the dates match
    return taskDateOnly.isAtSameMomentAs(selectedDateOnly);
  }

  // Update the map of dates that have tasks
  void _updateDatesWithTasks(List<schedule.Datum> tasks) {
    datesWithTasks.clear();

    for (var task in tasks) {
      if (task.scheduledTimeStamp != null &&
          task.taskStatus == "Confirmed" &&
          task.isOpen == false) {
        final taskDate = task.scheduledTimeStamp!;
        final dateKey = "${taskDate.year}-${taskDate.month}-${taskDate.day}";

        datesWithTasks[dateKey] = true;
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ScheduleTaskCubit, ScheduleTaskState>(
      listener: (context, state) {
        if (state is ScheduleTaskSuccess) {
          var response = state.response;

          setState(() {
            // Store all tasks from the API response
            allApiTasks = response.data?["add_tasks"] ?? [];

            // Update the map of dates with tasks
            _updateDatesWithTasks(allApiTasks);
          });

          // Filter tasks for the selected date
          _filterTasksForSelectedDate();
        } else if (state is ScheduleTaskError) {
          // Show error message
          SnackBarService.error(
            context: context,
            message: state.message,
          );
        }
      },
      builder: (context, state) {
        return Scaffold(
          backgroundColor: AppColors.lightGrey2,
          appBar: _buildAppBar(),
          body: state is ScheduleTaskLoading
              ? const Center(child: CircularProgressIndicator())
              : state is ScheduleTaskError
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.error_outline,
                            color: Colors.red,
                            size: 48,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error loading tasks',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton(
                            onPressed: _initializeData,
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    )
                  : RefreshIndicator(
                      onRefresh: _initializeData,
                      child: _buildScrollableContent(context),
                    ),
          floatingActionButton: _isCheckboxMode && selectedItems.isNotEmpty
              ? Container(
                  height: 56,
                  decoration: BoxDecoration(
                    color: AppColors.midGrey,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  padding: const EdgeInsets.all(8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _isCheckboxMode = false;
                            selectedItems.clear();
                            _areAllItemsSelected = false;
                          });
                        },
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Icon(
                            Icons.close_rounded,
                            color: AppColors.black,
                          ),
                        ),
                      ),
                      const Gap(8),
                      SizedBox(
                        width: 96,
                        child: AppButton(
                          text: _areAllItemsSelected
                              ? "Deselect all"
                              : "Select all",
                          color: Colors.white,
                          textColor: AppColors.black,
                          onPressed: _selectAllItems,
                          height: 40,
                        ),
                      ),
                      const Gap(8),
                      SizedBox(
                        width: 96,
                        child: AppButton(
                          text: "Reschedule",
                          color: AppColors.primaryBlue,
                          onPressed: () {
                            // Show calendar bottom sheet
                            _showCalendarBottomSheet(context);
                          },
                          height: 40,
                        ),
                      ),
                    ],
                  ),
                )
              : null,
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerFloat,
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      title: 'Scheduled',
      bottom: _buildViewToggleButton(),
      actions: [
        GestureDetector(
          child: Image.asset(
            AppAssets.appbarCalendar,
            scale: 4,
            color: !_isCheckboxMode ? AppColors.primaryBlue : AppColors.black,
          ),
          onTap: () {
            setState(() {
              _isCheckboxMode = false;
            });
          },
        ),
        const Gap(8),
        GestureDetector(
          child: Image.asset(
            AppAssets.appbarCalendarEdit,
            color: _isCheckboxMode ? AppColors.primaryBlue : AppColors.black,
            scale: 4,
          ),
          onTap: () {
            setState(() {
              _isCheckboxMode = !_isCheckboxMode;
              if (!_isCheckboxMode) {
                // Clear selection when exiting checkbox mode
                selectedItems.clear();
                _areAllItemsSelected = false;
              }
            });
          },
        ),
        const Gap(8),
        GestureDetector(
          child: Image.asset(
            AppAssets.appbarMap,
            scale: 4,
          ),
          onTap: () {
            context.router.push(const JourneyMapRoute());
          },
        ),
        const Gap(16)
      ],
    );
  }

  void _handleParentActionTap(String actionType, Datum task) {
    switch (actionType) {
      case ActionTypes.contactInfo:
        SnackBarService.info(context: context, message: 'Contact info');
        break;
      case ActionTypes.map:
        SnackBarService.info(context: context, message: 'Map');
        break;
      case ActionTypes.chatAssistant:
        SnackBarService.info(context: context, message: 'Chat with assistant');
        break;
      default:
        SnackBarService.info(context: context, message: 'Unknown action');
    }
  }

  // Implementation of select all that works directly with the data
  void _selectAllItems() {
    setState(() {
      _areAllItemsSelected = !_areAllItemsSelected;

      if (_areAllItemsSelected) {
        // We're selecting all items - create a fresh list with all scheduled tasks
        selectedItems = List.from(scheduledTasks);
      } else {
        // We're deselecting all - clear the list
        selectedItems.clear();
      }
    });
  }

  void _handleSelectionChanged(List<Datum> selectedItems) {
    setState(() {
      this.selectedItems = selectedItems;
      // Update _areAllItemsSelected based on whether all items are selected
      _areAllItemsSelected = selectedItems.length == scheduledTasks.length &&
          scheduledTasks.isNotEmpty;
    });
  }

  void _handleSubtaskActionTap(String actionType, Datum task) {
    switch (actionType) {
      case ActionTypes.changeHelper:
        // Only enable if taskCount > 1 and teamlead == 1
        if (task.taskCount != null &&
            task.teamlead != null &&
            task.taskCount! > 1 &&
            task.teamlead == 1) {
          SnackBarService.info(context: context, message: 'Change helper');
        } else {
          SnackBarService.info(
              context: context,
              message: 'Change helper not available for this task');
        }
        break;
      case ActionTypes.viewDocument:
        // Check if task has documents
        if (task.documents != null) {
          SnackBarService.info(context: context, message: 'View document');
        } else {
          SnackBarService.info(
              context: context,
              message: 'No documents available for this task');
        }
        break;
      case ActionTypes.viewPos:
        // Only enable if posRequired is true
        if (task.posRequired == true) {
          SnackBarService.info(context: context, message: 'View POS');
        } else {
          SnackBarService.info(
              context: context, message: 'POS not required for this task');
        }
        break;
      case ActionTypes.viewNote:
        // Only enable if taskNote exists or notes isn't empty
        if ((task.taskNote != null && task.taskNote!.isNotEmpty)) {
          SnackBarService.info(context: context, message: 'View note');
        } else {
          SnackBarService.info(
              context: context, message: 'No notes available for this task');
        }
        break;
      case ActionTypes.viewBrief:
        // Only enable if task has forms with questions containing non-empty questionBrief
        bool hasBrief = false;
        if (task.forms != null) {
          hasBrief = task.forms!.any((form) {
            if (form.questions != null) {
              return form.questions!.any((question) =>
                  question.questionBrief != null &&
                  question.questionBrief!.isNotEmpty);
            }
            return false;
          });
        }

        if (hasBrief) {
          SnackBarService.info(context: context, message: 'View brief');
        } else {
          SnackBarService.info(
              context: context, message: 'No brief available for this task');
        }
        break;
      case ActionTypes.chatAssistant:
        SnackBarService.info(context: context, message: 'Chat with assistant');
        break;
      default:
        SnackBarService.info(context: context, message: 'Unknown action');
    }
  }

  PreferredSizeWidget _buildViewToggleButton() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(48),
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            // Tab bar with All, This Week, Next Week, and Overdue buttons
            Expanded(
              child: Container(
                height: 40,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: const Color(0xFFE0E0E0),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    // All button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _selectedButton = 'all';
                            _tabController.animateTo(0);
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          elevation: 0,
                          backgroundColor: _selectedButton == 'all'
                              ? AppColors.primaryBlue
                              : const Color(0xFFF5F5F5),
                          foregroundColor: _selectedButton == 'all'
                              ? Colors.white
                              : AppColors.blackTint1,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(8),
                              bottomLeft: Radius.circular(8),
                            ),
                            side: BorderSide(
                              color: Colors.grey,
                              width: 0,
                            ),
                          ),
                          padding: EdgeInsets.zero,
                        ),
                        child: Text(
                          'All',
                          style: Theme.of(context)
                              .textTheme
                              .montserratNavigationPrimaryMedium
                              .copyWith(
                                color: _selectedButton == 'all'
                                    ? Colors.white
                                    : Colors.black,
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                        ),
                      ),
                    ),

                    // This Week button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _selectedButton = 'this_week';
                            _tabController.animateTo(1);
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          elevation: 0,
                          backgroundColor: _selectedButton == 'this_week'
                              ? AppColors.primaryBlue
                              : const Color(0xFFF5F5F5),
                          foregroundColor: _selectedButton == 'this_week'
                              ? Colors.white
                              : AppColors.blackTint1,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.zero,
                            side: BorderSide(
                              color: Colors.grey,
                              width: 0,
                            ),
                          ),
                          padding: EdgeInsets.zero,
                        ),
                        child: Text(
                          'This week',
                          style: Theme.of(context)
                              .textTheme
                              .montserratNavigationPrimaryMedium
                              .copyWith(
                                color: _selectedButton == 'this_week'
                                    ? Colors.white
                                    : Colors.black,
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                        ),
                      ),
                    ),

                    // Next Week button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _selectedButton = 'next_week';
                            _tabController.animateTo(2);
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          elevation: 0,
                          backgroundColor: _selectedButton == 'next_week'
                              ? AppColors.primaryBlue
                              : const Color(0xFFF5F5F5),
                          foregroundColor: _selectedButton == 'next_week'
                              ? Colors.white
                              : AppColors.blackTint1,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.zero,
                            side: BorderSide(
                              color: Colors.grey,
                              width: 0,
                            ),
                          ),
                          padding: EdgeInsets.zero,
                        ),
                        child: Text(
                          'Next week',
                          style: Theme.of(context)
                              .textTheme
                              .montserratNavigationPrimaryMedium
                              .copyWith(
                                color: _selectedButton == 'next_week'
                                    ? Colors.white
                                    : Colors.black,
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                        ),
                      ),
                    ),

                    // Overdue button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          setState(() {
                            _selectedButton = 'overdue';
                            _tabController.animateTo(3);
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          elevation: 0,
                          backgroundColor: _selectedButton == 'overdue'
                              ? AppColors.primaryBlue
                              : const Color(0xFFF5F5F5),
                          foregroundColor: _selectedButton == 'overdue'
                              ? Colors.white
                              : AppColors.blackTint1,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(8),
                              bottomRight: Radius.circular(8),
                            ),
                            side: BorderSide(
                              color: Colors.grey,
                              width: 0,
                            ),
                          ),
                          padding: EdgeInsets.zero,
                        ),
                        child: Text(
                          'Overdue',
                          style: Theme.of(context)
                              .textTheme
                              .montserratNavigationPrimaryMedium
                              .copyWith(
                                color: _selectedButton == 'overdue'
                                    ? Colors.white
                                    : Colors.black,
                                fontWeight: FontWeight.w600,
                                fontSize: 12,
                              ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build the scrollable content that includes task list
  Widget _buildScrollableContent(BuildContext context) {
    return SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Content section based on selected button
          if (_selectedButton == 'all')
            // For "All" tab, show all scheduled tasks
            _buildAllTasksListContent()
          else if (_selectedButton == 'this_week' ||
              _selectedButton == 'next_week')
            // For week tabs, show tasks for the selected week
            _buildTaskListContent()
          else if (_selectedButton == 'overdue')
            // For overdue tab, show overdue tasks
            _buildOverdueTaskList()
          else
            // Fallback for any other state
            Container(
              color: AppColors.lightGrey2,
              padding: const EdgeInsets.all(16),
              child: Center(
                child: Text(
                  'Select a view option',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Build all tasks list content for menu button
  Widget _buildAllTasksListContent() {
    // For the "All" tab, show all scheduled tasks without date filtering
    List<Datum> tasksToShow = [];

    // Get all confirmed tasks that are not open
    tasksToShow = allApiTasks
        .where((task) =>
            task.taskStatus == "Confirmed" &&
            task.isOpen == false &&
            task.scheduledTimeStamp != null)
        .toList()
      // Sort by scheduled date and time
      ..sort((a, b) => (a.scheduledTimeStamp ?? DateTime.now())
          .compareTo(b.scheduledTimeStamp ?? DateTime.now()));

    if (tasksToShow.isEmpty) {
      return buildEmptyState('No scheduled tasks available', context);
    }

    // For menu button, show the regular list with all tasks
    return ReorderableStoreList(
      tasks: tasksToShow,
      isCalendarMode: _isCheckboxMode,
      showScheduledDate: true, // Show date in All tab
      showTickIndicator: true,
      showAllDisclosureIndicator: false,
      permanentlyDisableAllDisclosureIndicator: false,
      isOpenTask: true,
      onParentActionTap: _handleParentActionTap,
      onSubtaskActionTap: _handleSubtaskActionTap,
      onSelectionChanged: _handleSelectionChanged,
      selectAll: _areAllItemsSelected,
    );
  }

  // Build the task list content based on the current view (week or month)
  Widget _buildTaskListContent() {
    // For week view, show tasks grouped by date for the entire week
    if (isWeekView) {
      return _buildWeekTaskList();
    }
    // For month view, show all dates in the month with their tasks
    else {
      return _buildMonthTaskList();
    }
  }

  // Build the overdue task list
  Widget _buildOverdueTaskList() {
    // Get today's date at midnight for comparison
    final DateTime today =
        DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);

    // Filter tasks that are scheduled before today
    List<Datum> overdueTasks = allApiTasks
        .where((task) =>
            task.taskStatus == "Confirmed" &&
            task.isOpen == false &&
            task.scheduledTimeStamp != null &&
            task.scheduledTimeStamp!.isBefore(today))
        .toList()
      // Sort by scheduled date and time, then by store name
      ..sort((a, b) => (a.scheduledTimeStamp ?? DateTime.now())
          .compareTo(b.scheduledTimeStamp ?? DateTime.now()))
      ..sort((a, b) => (a.storeName ?? '').compareTo(b.storeName ?? ''));

    // If no overdue tasks, show empty state
    if (overdueTasks.isEmpty) {
      return buildEmptyState('No overdue tasks', context);
    }

    // Convert schedule.Datum to unscheduled Datum for compatibility with ReorderableStoreList
    List<Datum> convertedTasks =
        overdueTasks.map((task) => convertScheduleToDatum(task)).toList();

    // Return the list of overdue tasks
    return Container(
      constraints: const BoxConstraints(minHeight: 0),
      child: ReorderableStoreList(
        tasks: convertedTasks,
        isCalendarMode: _isCheckboxMode,
        showScheduledDate: true, // Show date for overdue tasks
        showTickIndicator: true,
        showAllDisclosureIndicator: false,
        permanentlyDisableAllDisclosureIndicator: false,
        isOpenTask: true,
        onParentActionTap: _handleParentActionTap,
        onSubtaskActionTap: _handleSubtaskActionTap,
        onSelectionChanged: _handleSelectionChanged,
        selectAll: _areAllItemsSelected,
      ),
    );
  }

  // Build the month task list with dates as headers, similar to week view
  Widget _buildMonthTaskList() {
    final textTheme = Theme.of(context).textTheme;
    // Get tasks for the entire month
    Map<DateTime, List<Datum>> tasksByDate = _getTasksForMonth();

    // Check if there are any tasks for the entire month
    bool hasAnyTasks = false;
    tasksByDate.forEach((date, tasks) {
      if (tasks.isNotEmpty) {
        hasAnyTasks = true;
      }
    });

    // If no tasks for the entire month, show a message
    if (!hasAnyTasks) {
      return buildEmptyState('No tasks for this month', context);
    }

    // Create a list of widgets for each date with its tasks
    List<Widget> dateTaskWidgets = [];

    // Sort the dates
    List<DateTime> sortedDates = tasksByDate.keys.toList()
      ..sort((a, b) => a.compareTo(b));

    // Build widgets for each date
    for (DateTime date in sortedDates) {
      List<Datum> tasksForDate = tasksByDate[date]!;

      // Check if this date is today
      final bool isToday = date.day == DateTime.now().day &&
          date.month == DateTime.now().month &&
          date.year == DateTime.now().year;

      // Check if this date is a weekend
      final bool isWeekend = date.weekday >= 6; // Saturday or Sunday

      // Add date header with styling based on Figma design
      dateTaskWidgets.add(
        Container(
          width: double.infinity,
          color:
              tasksForDate.isNotEmpty ? Colors.transparent : Colors.transparent,
          padding: EdgeInsets.symmetric(
              horizontal: 16.0, vertical: tasksForDate.isNotEmpty ? 2 : 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Date with day name
              Text(
                DateFormat('EEE d MMM').format(date),
                style: textTheme.montserratTitleSmall.copyWith(
                    color: isToday
                        ? AppColors.primaryBlue
                        : isWeekend
                            ? Colors.grey
                            : isWeekend
                                ? Colors.grey
                                : tasksForDate.isEmpty
                                    ? AppColors.primaryBlue
                                    : Colors.black),
              ),

              // Task count with blue color for non-weekend days
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: tasksForDate.isEmpty
                    ? Text(
                        'No tasks',
                        style: textTheme.montserratTitleExtraSmall
                            .copyWith(color: AppColors.primaryBlue),
                      )
                    : Row(
                        children: [
                          Image.asset(AppAssets.appbarRecentTime,
                              scale: 4, color: Colors.black),
                          const Gap(4),
                          Text(
                            _calculateTotalHours(tasksForDate),
                            style: textTheme.montserratTitleExtraSmall.copyWith(
                                color: isWeekend
                                    ? Colors.grey
                                    : tasksForDate.isEmpty
                                        ? AppColors.primaryBlue
                                        : Colors.black),
                          ),
                        ],
                      ),
              ),
            ],
          ),
        ),
      );

      // Add divider
      tasksForDate.isEmpty
          ? dateTaskWidgets.add(const Divider(height: 1))
          : const SizedBox();

      // If there are tasks for this date, show them
      if (tasksForDate.isNotEmpty) {
        // Add tasks for this date - use ReorderableStoreList
        dateTaskWidgets.add(
          Container(
            constraints: const BoxConstraints(minHeight: 0),
            child: ReorderableStoreList(
              tasks: tasksForDate,
              isCalendarMode: _isCheckboxMode,
              showScheduledDate: false,
              showTickIndicator: true,
              showAllDisclosureIndicator: false,
              permanentlyDisableAllDisclosureIndicator: false,
              isOpenTask: true,
              onParentActionTap: _handleParentActionTap,
              onSubtaskActionTap: _handleSubtaskActionTap,
              onSelectionChanged: _handleSelectionChanged,
              selectAll: _areAllItemsSelected,
            ),
          ),
        );

        // Add divider after tasks
        dateTaskWidgets.add(const Divider(height: 1));
      }
    }

    // Return a Column with all date sections
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: dateTaskWidgets,
    );
  }

  // Build the week task list with dates as headers
  Widget _buildWeekTaskList() {
    final textTheme = Theme.of(context).textTheme;

    // Get tasks for the entire week
    Map<DateTime, List<Datum>> tasksByDate = _getTasksForWeek();

    // Check if there are any tasks for the entire week
    bool hasAnyTasks = false;
    tasksByDate.forEach((date, tasks) {
      if (tasks.isNotEmpty) {
        hasAnyTasks = true;
      }
    });

    // If no tasks for the entire week, show a message
    if (!hasAnyTasks) {
      return buildEmptyState('No tasks for this week', context);
    }

    // Create a list of widgets for each date with its tasks
    List<Widget> dateTaskWidgets = [];

    // Sort the dates
    List<DateTime> sortedDates = tasksByDate.keys.toList()
      ..sort((a, b) => a.compareTo(b));

    // Build widgets for each date
    for (DateTime date in sortedDates) {
      List<Datum> tasksForDate = tasksByDate[date]!;

      // Check if this date is today
      final bool isToday = date.day == DateTime.now().day &&
          date.month == DateTime.now().month &&
          date.year == DateTime.now().year;

      // Check if this date is a weekend
      final bool isWeekend = date.weekday >= 6; // Saturday or Sunday

      // Add date header with styling based on Figma design
      dateTaskWidgets.add(
        Container(
          width: double.infinity,
          color:
              tasksForDate.isNotEmpty ? Colors.transparent : Colors.transparent,
          padding: EdgeInsets.symmetric(
              horizontal: 16.0, vertical: tasksForDate.isNotEmpty ? 2 : 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Date with day name
              Text(
                DateFormat('EEE d MMM').format(date),
                style: textTheme.montserratTitleSmall.copyWith(
                    color: isToday
                        ? AppColors.primaryBlue
                        : isWeekend
                            ? Colors.grey
                            : isWeekend
                                ? Colors.grey
                                : tasksForDate.isEmpty
                                    ? AppColors.primaryBlue
                                    : Colors.black),
              ),

              // Task count with blue color for non-weekend days
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: tasksForDate.isEmpty
                    ? Text(
                        'No tasks',
                        style: textTheme.montserratTitleExtraSmall
                            .copyWith(color: AppColors.primaryBlue),
                      )
                    : Row(
                        children: [
                          Image.asset(
                            AppAssets.appbarRecentTime,
                            scale: 4,
                            color: Colors.black,
                          ),
                          const Gap(4),
                          Text(
                            _calculateTotalHours(tasksForDate),
                            style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: isWeekend
                                    ? Colors.grey
                                    : tasksForDate.isEmpty
                                        ? AppColors.primaryBlue
                                        : Colors.black),
                          ),
                        ],
                      ),
              ),
            ],
          ),
        ),
      );

      // Add divider
      tasksForDate.isEmpty
          ? dateTaskWidgets.add(const Divider(height: 1))
          : const SizedBox();

      // If no tasks for this date, show empty state message
      if (tasksForDate.isNotEmpty) {
        // Add tasks for this date - use StoreReorderableListNew instead of WeekTaskCard
        // Wrap in a Container with constraints to ensure proper layout
        dateTaskWidgets.add(
          Container(
            constraints: const BoxConstraints(minHeight: 0),
            child: ReorderableStoreList(
              tasks: tasksForDate,
              isCalendarMode: _isCheckboxMode,
              showScheduledDate: false,
              showTickIndicator: true,
              showAllDisclosureIndicator: false,
              permanentlyDisableAllDisclosureIndicator: false,
              isOpenTask: true,
              onParentActionTap: _handleParentActionTap,
              onSubtaskActionTap: _handleSubtaskActionTap,
              onSelectionChanged: _handleSelectionChanged,
              selectAll: _areAllItemsSelected,
            ),
          ),
        );
      }

      tasksForDate.isNotEmpty
          ? dateTaskWidgets.add(const Divider(height: 1))
          : const SizedBox();
    }

    // Return a Column with all date sections
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: dateTaskWidgets,
    );
  }

  // Get tasks for the entire week grouped by date, including days with no tasks
  Map<DateTime, List<Datum>> _getTasksForWeek() {
    Map<DateTime, List<Datum>> tasksByDate = {};

    // Initialize the map with all days in the week (empty lists)
    for (var weekDay in weekDays) {
      final normalizedDate = DateTime(weekDay.year, weekDay.month, weekDay.day);
      tasksByDate[normalizedDate] = [];
    }

    // Process all API tasks
    for (var task in allApiTasks) {
      // Check if the task is confirmed and not open
      if (task.taskStatus != "Confirmed" ||
          task.isOpen == true ||
          task.scheduledTimeStamp == null) {
        continue;
      }

      // Check if the task's date is in the current week
      for (var weekDay in weekDays) {
        final weekDayDate = DateTime(weekDay.year, weekDay.month, weekDay.day);
        final taskDate = DateTime(task.scheduledTimeStamp!.year,
            task.scheduledTimeStamp!.month, task.scheduledTimeStamp!.day);

        if (weekDayDate.isAtSameMomentAs(taskDate)) {
          // Create a normalized date (without time) for grouping
          final normalizedDate = DateTime(task.scheduledTimeStamp!.year,
              task.scheduledTimeStamp!.month, task.scheduledTimeStamp!.day);

          // Add to list
          tasksByDate[normalizedDate]!.add(task);
          break;
        }
      }
    }

    // Sort tasks within each date by store name and scheduled time
    tasksByDate.forEach((date, tasks) {
      tasks.sort((a, b) => (a.storeName ?? '').compareTo(b.storeName ?? ''));
      tasks.sort((a, b) => (a.scheduledTimeStamp ?? DateTime.now())
          .compareTo(b.scheduledTimeStamp ?? DateTime.now()));
    });

    return tasksByDate;
  }

  // Get tasks for the entire month grouped by date, including days with no tasks
  Map<DateTime, List<Datum>> _getTasksForMonth() {
    Map<DateTime, List<Datum>> tasksByDate = {};

    // Get the last day of the month
    final DateTime lastDayOfMonth =
        DateTime(_currentMonth.year, _currentMonth.month + 1, 0);

    // Initialize the map with all days in the month (empty lists)
    for (int day = 1; day <= lastDayOfMonth.day; day++) {
      final normalizedDate =
          DateTime(_currentMonth.year, _currentMonth.month, day);
      tasksByDate[normalizedDate] = [];
    }

    // Process all API tasks
    for (var task in allApiTasks) {
      // Check if the task is confirmed and not open
      if (task.taskStatus != "Confirmed" ||
          task.isOpen == true ||
          task.scheduledTimeStamp == null) {
        continue;
      }

      // Check if the task's date is in the current month
      final taskDate = DateTime(task.scheduledTimeStamp!.year,
          task.scheduledTimeStamp!.month, task.scheduledTimeStamp!.day);

      if (taskDate.year == _currentMonth.year &&
          taskDate.month == _currentMonth.month) {
        // Create a normalized date (without time) for grouping
        final normalizedDate =
            DateTime(taskDate.year, taskDate.month, taskDate.day);

        // Check if this date is in our map (it should be, but just to be safe)
        if (tasksByDate.containsKey(normalizedDate)) {
          // Add to list
          tasksByDate[normalizedDate]!.add(task);
        }
      }
    }

    // Sort tasks within each date by store name and scheduled time
    tasksByDate.forEach((date, tasks) {
      tasks.sort((a, b) => (a.storeName ?? '').compareTo(b.storeName ?? ''));
      tasks.sort((a, b) => (a.scheduledTimeStamp ?? DateTime.now())
          .compareTo(b.scheduledTimeStamp ?? DateTime.now()));
    });

    return tasksByDate;
  }

  // Calculate total hours for a list of tasks
  String _calculateTotalHours(List<Datum> tasks) {
    logger('tasks: ${tasks.length}');
    if (tasks.isEmpty) {
      return "0 hrs";
    }

    double totalMinutes = 0;
    for (var task in tasks) {
      if (task.minutes != null) {
        // Ensure we're properly converting the minutes value to a double
        // Handle different numeric types (int, double, num)
        try {
          logger(
              'Task minutes: ${task.budget}, type: ${task.budget.runtimeType}');
          totalMinutes += task.budget!.toDouble();
        } catch (e) {
          logger("Error converting minutes to double: ${e.toString()}");
          // If conversion fails, try parsing as string
          try {
            totalMinutes += double.parse(task.budget.toString());
          } catch (e) {
            logger("Error parsing minutes as string: ${e.toString()}");
          }
        }
      }
    }

    // Convert minutes to hours with proper decimal representation
    double hours = totalMinutes / 60;

    // Format hours to show proper fractions (e.g., 0.75 instead of 0.8 for 45 minutes)
    // For 45 minutes, we want to show 0.75 hrs instead of 0.8 hrs
    String formattedHours;
    if (totalMinutes % 60 == 45) {
      formattedHours =
          "${(totalMinutes ~/ 60)}.75"; // Use exact fraction for 45 minutes
    } else if (totalMinutes % 60 == 30) {
      formattedHours =
          "${(totalMinutes ~/ 60)}.5"; // Use exact fraction for 30 minutes
    } else if (totalMinutes % 60 == 15) {
      formattedHours =
          "${(totalMinutes ~/ 60)}.25"; // Use exact fraction for 15 minutes
    } else {
      formattedHours = hours.toStringAsFixed(2);
      // Remove trailing zeros
      if (formattedHours.endsWith('0')) {
        formattedHours = formattedHours.substring(0, formattedHours.length - 1);
      }
    }

    logger(
        'Total minutes: $totalMinutes, hours: $hours, formatted: $formattedHours');
    return "$formattedHours hrs";
  }

  // Add calendar bottom sheet functionality
  void _showCalendarBottomSheet(BuildContext context) {
    // Create a local reference to the cubit to avoid context usage in async gap
    final scheduleTaskCubit = context.read<ScheduleTaskCubit>();

    // Create a list of dates that have tasks
    List<DateTime> taskDates = [];
    for (var task in allApiTasks) {
      if (task.scheduledTimeStamp != null &&
          task.taskStatus == "Confirmed" &&
          task.isOpen != true) {
        taskDates.add(task.scheduledTimeStamp!);
      }
    }

    // Show calendar bottom sheet
    CalendarBottomSheet.show(
      context: context,
      calendarResponse: scheduleTaskCubit
          .calendarResponse, // Pass the calendar response from the cubit
      taskDates: taskDates, // Pass the task dates to show grey circles
    ).then((selectedDate) {
      // Check if the widget is still mounted and a date was selected
      if (selectedDate != null && mounted) {
        var tasklist = <Task>[];
        for (var item in selectedItems) {
          tasklist.add(Task(
            scheduledTimeStamp: selectedDate,
            taskId: item.taskId.toString(),
            submissionTimeStamp: DateTimeUtils.getCurrentTimeInSydney(),
            forceImportFollowupTask: false,
          ));
        }

        if (mounted) {
//           {
//   "submission_state": 1,
//   "task_status": "Confirmed",
//   "start_task_longitude": 0,
//   "minutes": 0,
//   "task_latitude": 0,
//   "followup_tasks": [
//     {
//       "followup_number": 0,
//       "schedule_note": "",
//       "followup_item_id": 0,
//       "task_id": "18486230",
//       "followup_type_id": 0,
//       "visit_date": "0001-01-01T00:00:00",
//       "budget": 0
//     }
//   ],
//   "appversion": "10.0.3",
//   "task_id": "18486230",
//   "claimable_kms": 0,
//   "task_longitude": 0,
//   "budget_calculated": 0,
//   "token": "eFDP4HJVSfCRCyld3UzJep:APA91bHfgUw9GaVjm4Wf25OeQbeKNCHPhSpuOUsdxUEte8A7k1l0uDdH8lotKkx_UeJ95efzCYy-TOMf2VNJ6n_518D6BDwC0y2QcE5IEPs40dqwXBYLcbQ",
//   "resume_pause_items": [],
//   "timer_minutes": 0,
//   "device_uid": "8b7a6774c878a206",
//   "pages": 0,
//   "submission_time_stamp": "2025-05-16T07:01:39",
//   "user_id": "75444",
//   "task_commencement_time_stamp": "0001-01-01T00:00:00",
//   "task_stopped_time_stamp": "0001-01-01T00:00:00",
//   "start_task_latitude": 0,
//   "comment": "",
//   "forms": [],
//   "scheduled_time_stamp": "2025-05-28T00:00:00"
// }
          var request = submit_report.SubmitReportRequestEntity(
            submissionState: 1,
            taskStatus: "Confirmed",
            scheduledTimeStamp: selectedDate,
            submissionTimeStamp: DateTimeUtils.getCurrentTimeInSydney(),
            taskId: selectedItems[0].taskId.toString(),
            userId: actualUserId,
            token: actualUserToken,
            appversion: actualAppVersion,
            deviceUid: actualDeviceUid,
            pages: 0,
            startTaskLatitude: 0,
            startTaskLongitude: 0,
            taskLatitude: 0,
            taskLongitude: 0,
            claimableKms: 0,
            budgetCalculated: 0,
            timerMinutes: 0,
            comment: "",
            forms: [],
            followupTasks: [
              submit_report.FollowupTask(
                followupNumber: 0,
                scheduleNote: "",
                followupItemId: 0,
                taskId: selectedItems[0].taskId.toString(),
                followupTypeId: 0,
                visitDate: DateTime(0001, 01, 01, 00, 00, 00),
                budget: 0,
              )
            ],
            resumePauseItems: [],
            taskCommencementTimeStamp: DateTime(0001, 01, 01, 00, 00, 00),
            taskStoppedTimeStamp: DateTime(0001, 01, 01, 00, 00, 00),
          );

          // Use the stored cubit reference instead of context
          scheduleTaskCubit.submitReport(request);

          // Refresh the data
          _initializeData();
        }
      }
    });
  }
}
