import 'package:storetrack_app/core/network/network_info.dart';

import '../../../../shared/models/result.dart';
import '../../data/datasources/home_local_datasource.dart';
import '../../data/datasources/home_remote_datasource.dart';
import '../entities/tasks_request_entity.dart';
import '../entities/tasks_response_entity.dart';
import '../../data/repositories/home_repository.dart';
import '../entities/calendar_response_entity.dart';
import '../usecases/get_calendar_usecase.dart';
import '../entities/submit_report_request_entity.dart';
import '../entities/submit_report_response_entity.dart';

class HomeRepositoryImpl implements HomeRepository {
  final HomeRemoteDataSource dataSource;
  final NetworkInfo networkInfo;
  final HomeLocalDataSource unscheduleLocalDataSource;

  HomeRepositoryImpl({
    required this.dataSource,
    required this.networkInfo,
    required this.unscheduleLocalDataSource,
  });

  @override
  Future<Result<TasksResponseEntity>> getTasks(
      TasksRequestEntity request) async {
    var result = await dataSource.getTasks(request);
    unscheduleLocalDataSource.saveUnscheduleResponse(result.data!);
    return dataSource.getTasks(request);
  }

  @override
  Future<Result<CalendarResponseEntity>> getCalendarData(
      GetCalendarParams request) async {
    return dataSource.getCalendarData(request);
  }

  @override
  Future<Result<SubmitReportResponseEntity>> submitReport(
      SubmitReportRequestEntity request) async {
    return dataSource.submitReport(request);
  }
}
