import 'package:bloc/bloc.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/features/home/<USER>/entities/calendar_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/get_calendar_usecase.dart';
import 'package:storetrack_app/features/home/<USER>/usecases/submit_report_usecase.dart';
import 'package:storetrack_app/shared/models/result.dart';

import '../../../domain/entities/tasks_request_entity.dart';
import '../../../domain/entities/tasks_response_entity.dart';
import '../../../domain/usecases/get_tasks_usecase.dart';
import 'unschedule_state.dart';

class UnscheduleTaskCubit extends Cubit<UnscheduleTaskState> {
  final GetTasksUseCase _getTasksUseCase;
  final GetCalendarUseCase _getCalendarUseCase;
  final SubmitReportUseCase _submitReportUseCase;

  UnscheduleTaskCubit(
    this._getTasksUseCase,
    this._getCalendarUseCase,
    this._submitReportUseCase,
  ) : super(UnscheduleTaskInitial());

  Future<void> getData(TasksRequestEntity request) async {
    emit(UnscheduleTaskLoading());

    try {
      final Result<TasksResponseEntity> tasksResult =
          await _getTasksUseCase(request);
      final Result<CalendarResponseEntity> calendarResult =
          await _getCalendarUseCase(GetCalendarParams(
        token: request.token,
        userId: request.userId,
      ));

      if (tasksResult.isSuccess && calendarResult.isSuccess) {
        emit(UnscheduleTaskSuccess(
          tasksResponse: tasksResult.data!,
          calendarResponse: calendarResult.data!,
        ));
      } else {
        final errorMessage = tasksResult.error?.toString() ??
            'Unknown error occurred while unscheduling tasks.';
        emit(UnscheduleTaskError(errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in UnscheduleTaskCubit: $e");
      emit(
          UnscheduleTaskError('An unexpected error occurred: ${e.toString()}'));
    }
  }

  void resetState() {
    emit(UnscheduleTaskInitial());
  }

  // Submit report for scheduling tasks
  Future<void> submitReport(SubmitReportRequestEntity request) async {
    emit(UnscheduleTaskLoading()); // Signal start of operation

    try {
      final Result<SubmitReportResponseEntity> result =
          await _submitReportUseCase.call(request);

      if (result.isSuccess) {
        // After successful submission, refresh the task list
        final tasksResult = await _getTasksUseCase(TasksRequestEntity(
          deviceUid: request.deviceUid ?? "",
          userId: request.userId ?? "",
          appversion: request.appversion ?? "",
          tasks: const [],
          token: request.token ?? "",
        ));

        final calendarResult = await _getCalendarUseCase(GetCalendarParams(
          token: request.token ?? "",
          userId: request.userId ?? "",
        ));

        if (tasksResult.isSuccess && calendarResult.isSuccess) {
          emit(UnscheduleTaskSuccess(
            tasksResponse: tasksResult.data!,
            calendarResponse: calendarResult.data!,
          ));
        } else {
          final errorMessage = tasksResult.error?.toString() ??
              'Unknown error occurred while refreshing tasks.';
          emit(UnscheduleTaskError(errorMessage));
        }
      } else {
        // Failure: Extract error message from Result
        final errorMessage = result.error?.toString() ??
            'Unknown error occurred while submitting report.';
        emit(UnscheduleTaskError(errorMessage));
      }
    } catch (e) {
      logger("Unexpected error in UnscheduleTaskCubit.submitReport: $e");
      emit(
          UnscheduleTaskError('An unexpected error occurred: ${e.toString()}'));
    }
  }
}
