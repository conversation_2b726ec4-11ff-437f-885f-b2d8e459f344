// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i17;
import 'package:flutter/material.dart' as _i18;
import 'package:storetrack_app/features/auth/presentation/pages/login_page.dart'
    as _i6;
import 'package:storetrack_app/features/auth/presentation/pages/reset_password_page.dart'
    as _i10;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as _i19;
import 'package:storetrack_app/features/home/<USER>/pages/assistant_page.dart'
    as _i1;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_holder_page.dart'
    as _i2;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_page.dart'
    as _i3;
import 'package:storetrack_app/features/home/<USER>/pages/home_page.dart'
    as _i4;
import 'package:storetrack_app/features/home/<USER>/pages/journey_map_page.dart'
    as _i5;
import 'package:storetrack_app/features/home/<USER>/pages/more_page.dart'
    as _i7;
import 'package:storetrack_app/features/home/<USER>/pages/profile_page.dart'
    as _i9;
import 'package:storetrack_app/features/home/<USER>/pages/scheduled_page.dart'
    as _i11;
import 'package:storetrack_app/features/home/<USER>/pages/task_details_page.dart'
    as _i13;
import 'package:storetrack_app/features/home/<USER>/pages/todays_page.dart'
    as _i14;
import 'package:storetrack_app/features/home/<USER>/pages/unscheduled_page.dart'
    as _i15;
import 'package:storetrack_app/features/notification/presentation/pages/notification_page.dart'
    as _i8;
import 'package:storetrack_app/features/splash/presentation/pages/splash_page.dart'
    as _i12;
import 'package:storetrack_app/features/web_browser/presentation/pages/web_browser_page.dart'
    as _i16;

/// generated route for
/// [_i1.AssistantPage]
class AssistantRoute extends _i17.PageRouteInfo<void> {
  const AssistantRoute({List<_i17.PageRouteInfo>? children})
      : super(
          AssistantRoute.name,
          initialChildren: children,
        );

  static const String name = 'AssistantRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      return const _i1.AssistantPage();
    },
  );
}

/// generated route for
/// [_i2.DashboardHolderPage]
class DashboardHolderRoute extends _i17.PageRouteInfo<void> {
  const DashboardHolderRoute({List<_i17.PageRouteInfo>? children})
      : super(
          DashboardHolderRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardHolderRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      return const _i2.DashboardHolderPage();
    },
  );
}

/// generated route for
/// [_i3.DashboardPage]
class DashboardRoute extends _i17.PageRouteInfo<void> {
  const DashboardRoute({List<_i17.PageRouteInfo>? children})
      : super(
          DashboardRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      return const _i3.DashboardPage();
    },
  );
}

/// generated route for
/// [_i4.HomePage]
class HomeRoute extends _i17.PageRouteInfo<void> {
  const HomeRoute({List<_i17.PageRouteInfo>? children})
      : super(
          HomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'HomeRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      return const _i4.HomePage();
    },
  );
}

/// generated route for
/// [_i5.JourneyMapPage]
class JourneyMapRoute extends _i17.PageRouteInfo<void> {
  const JourneyMapRoute({List<_i17.PageRouteInfo>? children})
      : super(
          JourneyMapRoute.name,
          initialChildren: children,
        );

  static const String name = 'JourneyMapRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      return const _i5.JourneyMapPage();
    },
  );
}

/// generated route for
/// [_i6.LoginPage]
class LoginRoute extends _i17.PageRouteInfo<void> {
  const LoginRoute({List<_i17.PageRouteInfo>? children})
      : super(
          LoginRoute.name,
          initialChildren: children,
        );

  static const String name = 'LoginRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      return const _i6.LoginPage();
    },
  );
}

/// generated route for
/// [_i7.MorePage]
class MoreRoute extends _i17.PageRouteInfo<void> {
  const MoreRoute({List<_i17.PageRouteInfo>? children})
      : super(
          MoreRoute.name,
          initialChildren: children,
        );

  static const String name = 'MoreRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      return const _i7.MorePage();
    },
  );
}

/// generated route for
/// [_i8.NotificationsPage]
class NotificationsRoute extends _i17.PageRouteInfo<void> {
  const NotificationsRoute({List<_i17.PageRouteInfo>? children})
      : super(
          NotificationsRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationsRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      return const _i8.NotificationsPage();
    },
  );
}

/// generated route for
/// [_i9.ProfilePage]
class ProfileRoute extends _i17.PageRouteInfo<void> {
  const ProfileRoute({List<_i17.PageRouteInfo>? children})
      : super(
          ProfileRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProfileRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      return const _i9.ProfilePage();
    },
  );
}

/// generated route for
/// [_i10.ResetPasswordPage]
class ResetPasswordRoute extends _i17.PageRouteInfo<ResetPasswordRouteArgs> {
  ResetPasswordRoute({
    _i18.Key? key,
    required String email,
    List<_i17.PageRouteInfo>? children,
  }) : super(
          ResetPasswordRoute.name,
          args: ResetPasswordRouteArgs(
            key: key,
            email: email,
          ),
          initialChildren: children,
        );

  static const String name = 'ResetPasswordRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ResetPasswordRouteArgs>();
      return _i10.ResetPasswordPage(
        key: args.key,
        email: args.email,
      );
    },
  );
}

class ResetPasswordRouteArgs {
  const ResetPasswordRouteArgs({
    this.key,
    required this.email,
  });

  final _i18.Key? key;

  final String email;

  @override
  String toString() {
    return 'ResetPasswordRouteArgs{key: $key, email: $email}';
  }
}

/// generated route for
/// [_i11.SchedulePage]
class ScheduleRoute extends _i17.PageRouteInfo<void> {
  const ScheduleRoute({List<_i17.PageRouteInfo>? children})
      : super(
          ScheduleRoute.name,
          initialChildren: children,
        );

  static const String name = 'ScheduleRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      return const _i11.SchedulePage();
    },
  );
}

/// generated route for
/// [_i12.SplashPage]
class SplashRoute extends _i17.PageRouteInfo<void> {
  const SplashRoute({List<_i17.PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      return const _i12.SplashPage();
    },
  );
}

/// generated route for
/// [_i13.TaskDetailsPage]
class TaskDetailsRoute extends _i17.PageRouteInfo<TaskDetailsRouteArgs> {
  TaskDetailsRoute({
    _i18.Key? key,
    required _i19.Datum task,
    List<_i17.PageRouteInfo>? children,
  }) : super(
          TaskDetailsRoute.name,
          args: TaskDetailsRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'TaskDetailsRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TaskDetailsRouteArgs>();
      return _i13.TaskDetailsPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class TaskDetailsRouteArgs {
  const TaskDetailsRouteArgs({
    this.key,
    required this.task,
  });

  final _i18.Key? key;

  final _i19.Datum task;

  @override
  String toString() {
    return 'TaskDetailsRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i14.TodayPage]
class TodayRoute extends _i17.PageRouteInfo<void> {
  const TodayRoute({List<_i17.PageRouteInfo>? children})
      : super(
          TodayRoute.name,
          initialChildren: children,
        );

  static const String name = 'TodayRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      return const _i14.TodayPage();
    },
  );
}

/// generated route for
/// [_i15.UnscheduledPage]
class UnscheduledRoute extends _i17.PageRouteInfo<void> {
  const UnscheduledRoute({List<_i17.PageRouteInfo>? children})
      : super(
          UnscheduledRoute.name,
          initialChildren: children,
        );

  static const String name = 'UnscheduledRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      return const _i15.UnscheduledPage();
    },
  );
}

/// generated route for
/// [_i16.WebBrowserPage]
class WebBrowserRoute extends _i17.PageRouteInfo<WebBrowserRouteArgs> {
  WebBrowserRoute({
    _i18.Key? key,
    required String url,
    List<_i17.PageRouteInfo>? children,
  }) : super(
          WebBrowserRoute.name,
          args: WebBrowserRouteArgs(
            key: key,
            url: url,
          ),
          initialChildren: children,
        );

  static const String name = 'WebBrowserRoute';

  static _i17.PageInfo page = _i17.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<WebBrowserRouteArgs>();
      return _i16.WebBrowserPage(
        key: args.key,
        url: args.url,
      );
    },
  );
}

class WebBrowserRouteArgs {
  const WebBrowserRouteArgs({
    this.key,
    required this.url,
  });

  final _i18.Key? key;

  final String url;

  @override
  String toString() {
    return 'WebBrowserRouteArgs{key: $key, url: $url}';
  }
}
