{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:4989293190837437690", "lastPropertyId": "48:7695162489770509703", "name": "Datum", "properties": [{"id": "1:7866617021410304017", "name": "id", "type": 6, "flags": 1}, {"id": "2:1917999897350191970", "name": "sentToPayroll", "type": 1}, {"id": "3:7187077056709341408", "name": "showKm", "type": 1}, {"id": "4:2023916243833773990", "name": "client", "type": 9}, {"id": "5:3402308568971675770", "name": "clientLogoUrl", "type": 9}, {"id": "6:7249967941954287075", "name": "storeGroup", "type": 9}, {"id": "7:304053710269816065", "name": "storeName", "type": 9}, {"id": "8:5250025709808758547", "name": "storeEmail", "type": 9}, {"id": "9:8767957361764176694", "name": "comment", "type": 9}, {"id": "10:178566632470600555", "name": "location", "type": 9}, {"id": "11:8088654016980929219", "name": "suburb", "type": 9}, {"id": "12:882861461136233436", "name": "cycle", "type": 9}, {"id": "13:3477306333226801362", "name": "canDelete", "type": 1}, {"id": "14:4729734751429998156", "name": "scheduledTimeStamp", "type": 10}, {"id": "15:7586890520791944816", "name": "submissionTimeStamp", "type": 10}, {"id": "16:1951837428018579443", "name": "expires", "type": 10}, {"id": "17:2734605590803593356", "name": "onTask", "type": 9}, {"id": "18:5738287678373096431", "name": "phone", "type": 9}, {"id": "19:6013716830216632692", "name": "rangeStart", "type": 10}, {"id": "20:8143288601352388700", "name": "rangeEnd", "type": 10}, {"id": "21:3793041385269087492", "name": "reOpened", "type": 1}, {"id": "22:6592628351744545318", "name": "reOpenedReason", "type": 9}, {"id": "23:987612093206478672", "name": "taskStatus", "type": 9}, {"id": "24:6753547410419319357", "name": "connoteUrl", "type": 9}, {"id": "25:3718150895511086951", "name": "posRequired", "type": 1}, {"id": "26:1357328974337357705", "name": "isPosMandatory", "type": 1}, {"id": "27:792590189407773463", "name": "posReceived", "type": 9}, {"id": "28:8769943795481164169", "name": "modifiedTimeStampDocuments", "type": 10}, {"id": "29:1813013136704241794", "name": "modifiedTimeStampForms", "type": 10}, {"id": "30:8351670303686273512", "name": "modifiedTimeStampMembers", "type": 10}, {"id": "31:798021831892894409", "name": "modifiedTimeStampTask", "type": 10}, {"id": "32:8482013567558532175", "name": "modifiedTimeStampPhotos", "type": 10}, {"id": "33:6866832247594888783", "name": "modifiedTimeStampSignatures", "type": 10}, {"id": "34:7929727129314586342", "name": "modifiedTimeStampSignaturetypes", "type": 10}, {"id": "35:761112182931492822", "name": "posSentTo", "type": 9}, {"id": "36:3749350646960920860", "name": "posSentToEmail", "type": 9}, {"id": "37:4859840613507767592", "name": "modifiedTimeStampPhototypes", "type": 10}, {"id": "38:4634278113742781728", "name": "taskCommencementTimeStamp", "type": 10}, {"id": "39:7486506732435461602", "name": "taskStoppedTimeStamp", "type": 10}, {"id": "40:1291126189314419769", "name": "taskNote", "type": 9}, {"id": "41:2674413479691419693", "name": "disallowReschedule", "type": 1}, {"id": "42:3873519663078325006", "name": "liveImagesOnly", "type": 1}, {"id": "43:8111976551720558409", "name": "timeSchedule", "type": 9}, {"id": "44:6374639905880152861", "name": "showFollowupIconMulti", "type": 1}, {"id": "45:3037716298768107755", "name": "followupSelectedMulti", "type": 1}, {"id": "46:1273187969640999677", "name": "isOpen", "type": 1}, {"id": "47:542456641046319776", "name": "preftime", "type": 9}, {"id": "48:7695162489770509703", "name": "sendTo", "type": 1}], "relations": []}], "lastEntityId": "1:4989293190837437690", "lastIndexId": "0:0", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [], "retiredRelationUids": [], "version": 1}