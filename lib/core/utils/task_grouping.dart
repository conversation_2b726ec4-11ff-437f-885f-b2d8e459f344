import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

/// Groups a list of [Datum] objects by their store_id.
/// Returns a list of lists where each inner list contains [Datum] objects with the same store_id.
List<List<Datum>> groupTasksByStore(List<Datum> tasks) {
  // Create a map to group tasks by store_id
  Map<int?, List<Datum>> groupedMap = {};

  // Group tasks by store_id
  for (var task in tasks) {
    if (!groupedMap.containsKey(task.storeId)) {
      groupedMap[task.storeId?.toInt()] = [];
    }
    groupedMap[task.storeId]!.add(task);
  }

  // Convert the map values to a list of lists
  return groupedMap.values.toList();
}
