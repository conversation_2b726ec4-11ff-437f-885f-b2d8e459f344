import 'dart:io';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/objectbox.g.dart';

class ObjectBox {
  /// The Store of this app.
  late final Store store;

  ObjectBox._create(this.store) {
    // Add any additional setup code, e.g. build queries.
  }

  /// Create an instance of ObjectBox to use throughout the app.
  static Future<ObjectBox> create() async {
    final docsDir = await getApplicationDocumentsDirectory();
    final dbDir = p.join(docsDir.path, "storetrack-db");

    // Delete the existing database directory to handle schema changes
    try {
      final dir = Directory(dbDir);
      if (await dir.exists()) {
        await dir.delete(recursive: true);
        logger("Deleted existing ObjectBox database due to schema changes");
      }
    } catch (e) {
      logger("Error deleting ObjectBox database: $e");
    }

    // Future<Store> openStore() {...} is defined in the generated objectbox.g.dart
    final store = await openStore(directory: dbDir);
    return ObjectBox._create(store);
  }
}
