name: storetrack_app
description: "Flutter application for Storetrack"

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.4.1 <4.0.0'

dependencies:
  cupertino_icons: ^1.0.6
  flutter:
    sdk: flutter
  dio: ^5.7.0
  pretty_dio_logger: ^1.4.0
  dio_smart_retry: ^6.0.0
  shared_preferences: ^2.2.2
  flutter_bloc: ^8.1.6
  intl: ^0.19.0
  cached_network_image: ^3.4.1
  get_it: ^8.0.0
  equatable: ^2.0.5
  internet_connection_checker_plus: ^2.5.2
  gap: ^3.0.1
  bloc: ^8.1.4
  flutter_native_splash: ^2.4.1
  rename: ^3.0.2
  icons_launcher: ^3.0.0
  auto_route: ^9.0.0
  url_launcher: ^6.3.1
  easy_debounce: ^2.0.3
  msal_auth: ^3.1.4
  dart_jsonwebtoken: ^2.17.0
  webview_flutter: ^4.10.0
  flutter_svg: ^2.0.9
  location: ^6.0.2
  table_calendar: ^3.0.9
  objectbox: ^4.1.0
  path: ^1.9.0
  path_provider: ^2.1.5
  objectbox_flutter_libs: ^4.1.0
  timezone: ^0.10.1
  google_maps_flutter: ^2.10.1
  flutter_polyline_points: ^2.1.0

dev_dependencies:
  auto_route_generator: ^9.0.0
  objectbox_generator: ^4.1.0
  build_runner:
  flutter_lints: ^3.0.0
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/fonts/
    - assets/jsons/
    - assets/icons/

  fonts:
    - family: Montserrat
      fonts:
        - asset: assets/fonts/Montserrat-Bold.ttf
          weight: 700
        - asset: assets/fonts/Montserrat-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Montserrat-Medium.ttf
          weight: 500
        - asset: assets/fonts/Montserrat-Regular.ttf
          weight: 400
        - asset: assets/fonts/Montserrat-Light.ttf
          weight: 300

# flutter_native_splash:
#   color: "#FF584F"
#   image: assets/images/g4l_icon.png
#   android_12:
#     color: "#FF584F"
#     image: assets/images/g4l_icon.png

# icons_launcher:
#   image_path: "assets/images/icon.png"
#   platforms:
#     android:
#       enable: true
#     ios:
#       enable: true
